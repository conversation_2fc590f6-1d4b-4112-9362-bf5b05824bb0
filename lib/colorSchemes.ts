import { scaleOrdinal } from 'd3-scale'
import {
  schemeCategory10,
  schemeSet1,
  schemeSet2,
  schemeSet3,
  schemeTableau10
} from 'd3-scale-chromatic'
import type { ColorScheme } from '../store/wordCloudConfigSlice'

// 颜色方案映射
const colorSchemeMap = {
  category10: schemeCategory10,
  category20: ["#1f77b4", "#aec7e8", "#ff7f0e", "#ffbb78", "#2ca02c", "#98df8a", "#d62728", "#ff9896", "#9467bd", "#c5b0d5", "#8c564b", "#c49c94", "#e377c2", "#f7b6d3", "#7f7f7f", "#c7c7c7", "#bcbd22", "#dbdb8d", "#17becf", "#9edae5"],
  category20b: ["#393b79", "#5254a3", "#6b6ecf", "#9c9ede", "#637939", "#8ca252", "#b5cf6b", "#cedb9c", "#8c6d31", "#bd9e39", "#e7ba52", "#e7cb94", "#843c39", "#ad494a", "#d6616b", "#e7969c", "#7b4173", "#a55194", "#ce6dbd", "#de9ed6"],
  category20c: ["#3182bd", "#6baed6", "#9ecae1", "#c6dbef", "#e6550d", "#fd8d3c", "#fdae6b", "#fdd0a2", "#31a354", "#74c476", "#a1d99b", "#c7e9c0", "#756bb1", "#9e9ac8", "#bcbddc", "#dadaeb", "#636363", "#969696", "#bdbdbd", "#d9d9d9"],
  pastel1: ["#fbb4ae", "#b3cde3", "#ccebc5", "#decbe4", "#fed9a6", "#ffffcc", "#e5d8bd", "#fddaec", "#f2f2f2"],
  pastel2: ["#b3e2cd", "#fdcdac", "#cbd5e8", "#f4cae4", "#e6f5c9", "#fff2ae", "#f1e2cc", "#cccccc"],
  set1: schemeSet1,
  set2: schemeSet2,
  set3: schemeSet3,
  tableau10: schemeTableau10,
  custom: [] // 自定义颜色将在运行时提供
}

/**
 * 根据颜色方案名称和自定义颜色创建颜色比例尺
 */
export function createColorScale(colorScheme: ColorScheme, customColors?: string[]) {
  if (colorScheme === 'custom' && customColors && customColors.length > 0) {
    return scaleOrdinal(customColors)
  }
  
  const colors = colorSchemeMap[colorScheme]
  if (!colors || colors.length === 0) {
    // 如果颜色方案不存在，回退到默认方案
    return scaleOrdinal(schemeCategory10)
  }
  
  return scaleOrdinal(colors)
}

/**
 * 获取颜色方案的预览颜色数组
 */
export function getColorSchemePreview(colorScheme: ColorScheme, customColors?: string[]): string[] {
  if (colorScheme === 'custom' && customColors && customColors.length > 0) {
    return customColors.slice(0, 5) // 只显示前5个颜色作为预览
  }
  
  const colors = colorSchemeMap[colorScheme]
  if (!colors || colors.length === 0) {
    return schemeCategory10.slice(0, 5)
  }
  
  return colors.slice(0, 5)
}
