import React, { useState } from "react"
import { useAppDispatch, useAppSelector } from "../store/store"
import {
  updateFontSettings,
  updateFontSizeRange,
  updateRotationSettings,
  updateColorScheme,
  updateLayoutSettings,
  updateAnimationSettings,
  updateDimensions,
  resetToDefaults,
  selectWordCloudConfig,
  COLOR_SCHEMES,
  FONT_FAMILIES,
  SPIRAL_TYPES,
  type ColorScheme,
  type FontFamily,
  type SpiralType
} from "../store/wordCloudConfigSlice"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card"
import { Label } from "./ui/label"
import { Slider } from "./ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select"
import { Checkbox } from "./ui/checkbox"
import { Button } from "./ui/button"
import { ColorPicker } from "./ui/color-picker"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "./ui/alert-dialog"
import { RotateCcw, Palette, Type, Settings, Layout, Zap, Eye } from "lucide-react"
import WordCloudComponent from "./wordcloud/WordCloud"

// 预览用的示例数据
const PREVIEW_WORDS = [
  { text: "React", size: 100, relatedPosts: [] },
  { text: "TypeScript", size: 80, relatedPosts: [] },
  { text: "JavaScript", size: 90, relatedPosts: [] },
  { text: "词云", size: 70, relatedPosts: [] },
  { text: "配置", size: 60, relatedPosts: [] },
  { text: "样式", size: 50, relatedPosts: [] },
  { text: "颜色", size: 45, relatedPosts: [] },
  { text: "字体", size: 40, relatedPosts: [] },
  { text: "布局", size: 35, relatedPosts: [] },
  { text: "动画", size: 30, relatedPosts: [] },
  { text: "预览", size: 25, relatedPosts: [] },
  { text: "设置", size: 20, relatedPosts: [] }
]

export function WordCloudSettings() {
  const dispatch = useAppDispatch()
  const config = useAppSelector(selectWordCloudConfig)
  const [activeSection, setActiveSection] = useState<string>("font")
  const [showPreview, setShowPreview] = useState<boolean>(true)

  const handleResetToDefaults = () => {
    dispatch(resetToDefaults())
  }

  const sections = [
    { id: "font", label: "字体设置", icon: Type },
    { id: "colors", label: "颜色方案", icon: Palette },
    { id: "layout", label: "布局设置", icon: Layout },
    { id: "animation", label: "动画效果", icon: Zap },
    { id: "advanced", label: "高级设置", icon: Settings }
  ]

  return (
    <div className="space-y-6">
      {/* 页面标题和控制按钮 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">词云样式配置</h2>
          <p className="text-muted-foreground">自定义词云的显示样式和行为</p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => setShowPreview(!showPreview)}
            className="flex items-center gap-2"
          >
            <Eye className="h-4 w-4" />
            {showPreview ? "隐藏预览" : "显示预览"}
          </Button>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                variant="outline"
                className="flex items-center gap-2"
              >
                <RotateCcw className="h-4 w-4" />
                重置默认
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>确认重置配置</AlertDialogTitle>
                <AlertDialogDescription>
                  这将重置所有词云配置到默认值。此操作无法撤销，您确定要继续吗？
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>取消</AlertDialogCancel>
                <AlertDialogAction onClick={handleResetToDefaults}>
                  确认重置
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      <div className="flex gap-6">
        {/* 侧边栏导航 */}
        <div className="w-48">
          <nav className="space-y-1">
            {sections.map((section) => {
              const Icon = section.icon
              return (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`w-full flex items-center gap-3 px-3 py-2 text-left rounded-lg transition-colors ${
                    activeSection === section.id
                      ? "bg-primary text-primary-foreground"
                      : "hover:bg-muted"
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  {section.label}
                </button>
              )
            })}
          </nav>
        </div>

        {/* 主配置区域 */}
        <div className={showPreview ? "flex-1" : "flex-1"}>
          {/* 预览区域 */}
          {showPreview && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  实时预览
                </CardTitle>
                <CardDescription>
                  预览当前配置的词云效果，配置更改会立即反映在预览中
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex justify-center">
                  <WordCloudComponent words={PREVIEW_WORDS} />
                </div>
              </CardContent>
            </Card>
          )}
          {activeSection === "font" && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Type className="h-5 w-5" />
                  字体设置
                </CardTitle>
                <CardDescription>配置词云中文字的字体样式和大小</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* 字体族 */}
                <div className="space-y-2">
                  <Label>字体族</Label>
                  <Select
                    value={config.fontFamily}
                    onValueChange={(value: FontFamily) =>
                      dispatch(updateFontSettings({ fontFamily: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(FONT_FAMILIES).map(([key, value]) => (
                        <SelectItem key={key} value={key}>
                          <span style={{ fontFamily: value }}>{value}</span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* 字体样式 */}
                <div className="space-y-2">
                  <Label>字体样式</Label>
                  <Select
                    value={config.fontStyle}
                    onValueChange={(value: "normal" | "italic" | "oblique") =>
                      dispatch(updateFontSettings({ fontStyle: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="normal">正常</SelectItem>
                      <SelectItem value="italic">斜体</SelectItem>
                      <SelectItem value="oblique">倾斜</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* 字体粗细 */}
                <div className="space-y-2">
                  <Label>字体粗细</Label>
                  <Select
                    value={config.fontWeight.toString()}
                    onValueChange={(value) =>
                      dispatch(updateFontSettings({ 
                        fontWeight: isNaN(Number(value)) ? value as any : Number(value)
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="normal">正常</SelectItem>
                      <SelectItem value="bold">粗体</SelectItem>
                      <SelectItem value="lighter">较细</SelectItem>
                      <SelectItem value="bolder">较粗</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* 字体大小范围 */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>最小字体大小: {config.minFontSize}px</Label>
                    <Slider
                      value={[config.minFontSize]}
                      onValueChange={([value]) =>
                        dispatch(updateFontSizeRange({ minFontSize: value }))
                      }
                      min={8}
                      max={32}
                      step={1}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>最大字体大小: {config.maxFontSize}px</Label>
                    <Slider
                      value={[config.maxFontSize]}
                      onValueChange={([value]) =>
                        dispatch(updateFontSizeRange({ maxFontSize: value }))
                      }
                      min={config.minFontSize + 1}
                      max={100}
                      step={1}
                    />
                  </div>
                </div>

                {/* 旋转设置 */}
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="rotation-enabled"
                      checked={config.rotationEnabled}
                      onCheckedChange={(checked) =>
                        dispatch(updateRotationSettings({ rotationEnabled: !!checked }))
                      }
                    />
                    <Label htmlFor="rotation-enabled">启用文字旋转</Label>
                  </div>
                  
                  {config.rotationEnabled && (
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="random-rotation"
                          checked={config.randomRotation}
                          onCheckedChange={(checked) =>
                            dispatch(updateRotationSettings({ randomRotation: !!checked }))
                          }
                        />
                        <Label htmlFor="random-rotation">随机旋转角度</Label>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {activeSection === "colors" && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="h-5 w-5" />
                  颜色方案
                </CardTitle>
                <CardDescription>选择词云的颜色主题和配色方案</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* 颜色方案选择 */}
                <div className="space-y-2">
                  <Label>预设颜色方案</Label>
                  <Select
                    value={config.colorScheme}
                    onValueChange={(value: ColorScheme) =>
                      dispatch(updateColorScheme({ colorScheme: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="category10">Category 10</SelectItem>
                      <SelectItem value="category20">Category 20</SelectItem>
                      <SelectItem value="pastel1">Pastel 1</SelectItem>
                      <SelectItem value="pastel2">Pastel 2</SelectItem>
                      <SelectItem value="set1">Set 1</SelectItem>
                      <SelectItem value="set2">Set 2</SelectItem>
                      <SelectItem value="set3">Set 3</SelectItem>
                      <SelectItem value="tableau10">Tableau 10</SelectItem>
                      <SelectItem value="custom">自定义</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* 自定义颜色 */}
                {config.colorScheme === "custom" && (
                  <div className="space-y-4">
                    <Label>自定义颜色</Label>
                    <div className="grid grid-cols-2 gap-4">
                      {config.customColors.map((color, index) => (
                        <ColorPicker
                          key={index}
                          value={color}
                          onChange={(newColor) => {
                            const newColors = [...config.customColors]
                            newColors[index] = newColor
                            dispatch(updateColorScheme({ customColors: newColors }))
                          }}
                          label={`颜色 ${index + 1}`}
                        />
                      ))}
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const newColors = [...config.customColors, "#000000"]
                          dispatch(updateColorScheme({ customColors: newColors }))
                        }}
                      >
                        添加颜色
                      </Button>
                      {config.customColors.length > 1 && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const newColors = config.customColors.slice(0, -1)
                            dispatch(updateColorScheme({ customColors: newColors }))
                          }}
                        >
                          删除颜色
                        </Button>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {activeSection === "layout" && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Layout className="h-5 w-5" />
                  布局设置
                </CardTitle>
                <CardDescription>配置词云的布局算法和间距</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* 螺旋类型 */}
                <div className="space-y-2">
                  <Label>布局算法</Label>
                  <Select
                    value={config.spiralType}
                    onValueChange={(value: SpiralType) =>
                      dispatch(updateLayoutSettings({ spiralType: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="archimedean">阿基米德螺旋</SelectItem>
                      <SelectItem value="rectangular">矩形螺旋</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* 词语间距 */}
                <div className="space-y-2">
                  <Label>词语间距: {config.padding}px</Label>
                  <Slider
                    value={[config.padding]}
                    onValueChange={([value]) =>
                      dispatch(updateLayoutSettings({ padding: value }))
                    }
                    min={0}
                    max={20}
                    step={1}
                  />
                </div>

                {/* 布局密度 */}
                <div className="space-y-2">
                  <Label>布局密度: {Math.round(config.layoutDensity * 100)}%</Label>
                  <Slider
                    value={[config.layoutDensity]}
                    onValueChange={([value]) =>
                      dispatch(updateLayoutSettings({ layoutDensity: value }))
                    }
                    min={0.1}
                    max={1}
                    step={0.1}
                  />
                </div>

                {/* 尺寸设置 */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>宽度: {config.width}px</Label>
                    <Slider
                      value={[config.width]}
                      onValueChange={([value]) =>
                        dispatch(updateDimensions({ width: value }))
                      }
                      min={200}
                      max={1200}
                      step={50}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>高度: {config.height}px</Label>
                    <Slider
                      value={[config.height]}
                      onValueChange={([value]) =>
                        dispatch(updateDimensions({ height: value }))
                      }
                      min={150}
                      max={800}
                      step={50}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {activeSection === "animation" && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  动画效果
                </CardTitle>
                <CardDescription>配置词云生成和交互动画</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* 启用动画 */}
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="animation-enabled"
                    checked={config.animationEnabled}
                    onCheckedChange={(checked) =>
                      dispatch(updateAnimationSettings({ animationEnabled: !!checked }))
                    }
                  />
                  <Label htmlFor="animation-enabled">启用动画效果</Label>
                </div>

                {/* 动画持续时间 */}
                {config.animationEnabled && (
                  <div className="space-y-2">
                    <Label>动画持续时间: {config.animationDuration}ms</Label>
                    <Slider
                      value={[config.animationDuration]}
                      onValueChange={([value]) =>
                        dispatch(updateAnimationSettings({ animationDuration: value }))
                      }
                      min={200}
                      max={3000}
                      step={100}
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {activeSection === "advanced" && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  高级设置
                </CardTitle>
                <CardDescription>高级配置选项和实验性功能</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* 随机种子 */}
                <div className="space-y-2">
                  <Label>随机种子: {config.randomSeed.toFixed(2)}</Label>
                  <Slider
                    value={[config.randomSeed]}
                    onValueChange={([value]) =>
                      dispatch(updateConfig({ randomSeed: value }))
                    }
                    min={0}
                    max={1}
                    step={0.01}
                  />
                  <p className="text-sm text-muted-foreground">
                    调整随机种子可以改变词云的布局，相同的种子会产生相同的布局
                  </p>
                </div>

                {/* 旋转角度自定义 */}
                {config.rotationEnabled && !config.randomRotation && (
                  <div className="space-y-2">
                    <Label>自定义旋转角度</Label>
                    <div className="text-sm text-muted-foreground mb-2">
                      当前角度: {config.rotationAngles.join(", ")}°
                    </div>
                    <p className="text-sm text-muted-foreground">
                      可以在这里添加自定义旋转角度的控件
                    </p>
                  </div>
                )}

                {/* 配置导入导出 */}
                <div className="space-y-4">
                  <Label>配置管理</Label>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const configJson = JSON.stringify(config, null, 2)
                        navigator.clipboard.writeText(configJson)
                        // 这里可以添加一个toast通知
                      }}
                    >
                      导出配置
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        // 这里可以添加导入配置的功能
                        console.log("导入配置功能待实现")
                      }}
                    >
                      导入配置
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
