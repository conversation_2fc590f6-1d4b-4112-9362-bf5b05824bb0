import { createSlice, PayloadAction } from "@reduxjs/toolkit"

// 颜色方案定义
export const COLOR_SCHEMES = {
  category10: "category10",
  category20: "category20", 
  category20b: "category20b",
  category20c: "category20c",
  pastel1: "pastel1",
  pastel2: "pastel2",
  set1: "set1",
  set2: "set2",
  set3: "set3",
  tableau10: "tableau10",
  custom: "custom"
} as const

export type ColorScheme = keyof typeof COLOR_SCHEMES

// 字体族选项
export const FONT_FAMILIES = {
  serif: "serif",
  sansSerif: "sans-serif", 
  monospace: "monospace",
  arial: "Arial",
  helvetica: "Helvetica",
  times: "Times",
  courier: "Courier",
  georgia: "Georgia",
  verdana: "Verdana"
} as const

export type FontFamily = keyof typeof FONT_FAMILIES

// 螺旋类型
export const SPIRAL_TYPES = {
  archimedean: "archimedean",
  rectangular: "rectangular"
} as const

export type SpiralType = keyof typeof SPIRAL_TYPES

// 词云配置接口
export interface WordCloudConfig {
  // 文字样式
  fontFamily: FontFamily
  fontStyle: "normal" | "italic" | "oblique"
  fontWeight: "normal" | "bold" | "lighter" | "bolder" | number
  
  // 字体大小范围
  minFontSize: number
  maxFontSize: number
  
  // 旋转角度设置
  rotationEnabled: boolean
  rotationAngles: number[] // 可选的旋转角度数组
  randomRotation: boolean // 是否随机旋转
  
  // 颜色方案
  colorScheme: ColorScheme
  customColors: string[] // 自定义颜色数组
  
  // 布局设置
  spiralType: SpiralType
  padding: number
  layoutDensity: number // 布局密度 (0-1)
  
  // 动画效果
  animationEnabled: boolean
  animationDuration: number
  
  // 尺寸设置
  width: number
  height: number
  
  // 其他设置
  randomSeed: number // 随机种子，用于确保布局一致性
}

// 默认配置
export const defaultWordCloudConfig: WordCloudConfig = {
  // 文字样式
  fontFamily: "sansSerif",
  fontStyle: "normal",
  fontWeight: "bold",
  
  // 字体大小范围
  minFontSize: 12,
  maxFontSize: 48,
  
  // 旋转角度设置
  rotationEnabled: true,
  rotationAngles: [-30, -15, 0, 15, 30],
  randomRotation: true,
  
  // 颜色方案
  colorScheme: "category10",
  customColors: ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd"],
  
  // 布局设置
  spiralType: "archimedean",
  padding: 5,
  layoutDensity: 0.7,
  
  // 动画效果
  animationEnabled: true,
  animationDuration: 1000,
  
  // 尺寸设置
  width: 400,
  height: 300,
  
  // 其他设置
  randomSeed: 0.5
}

// 创建slice
export const wordCloudConfigSlice = createSlice({
  name: "wordCloudConfig",
  initialState: defaultWordCloudConfig,
  reducers: {
    // 更新字体设置
    updateFontSettings: (state, action: PayloadAction<{
      fontFamily?: FontFamily
      fontStyle?: "normal" | "italic" | "oblique"
      fontWeight?: "normal" | "bold" | "lighter" | "bolder" | number
    }>) => {
      Object.assign(state, action.payload)
    },
    
    // 更新字体大小范围
    updateFontSizeRange: (state, action: PayloadAction<{
      minFontSize?: number
      maxFontSize?: number
    }>) => {
      if (action.payload.minFontSize !== undefined) {
        state.minFontSize = Math.max(1, action.payload.minFontSize)
      }
      if (action.payload.maxFontSize !== undefined) {
        state.maxFontSize = Math.max(state.minFontSize + 1, action.payload.maxFontSize)
      }
    },
    
    // 更新旋转设置
    updateRotationSettings: (state, action: PayloadAction<{
      rotationEnabled?: boolean
      rotationAngles?: number[]
      randomRotation?: boolean
    }>) => {
      Object.assign(state, action.payload)
    },
    
    // 更新颜色方案
    updateColorScheme: (state, action: PayloadAction<{
      colorScheme?: ColorScheme
      customColors?: string[]
    }>) => {
      Object.assign(state, action.payload)
    },
    
    // 更新布局设置
    updateLayoutSettings: (state, action: PayloadAction<{
      spiralType?: SpiralType
      padding?: number
      layoutDensity?: number
    }>) => {
      Object.assign(state, action.payload)
    },
    
    // 更新动画设置
    updateAnimationSettings: (state, action: PayloadAction<{
      animationEnabled?: boolean
      animationDuration?: number
    }>) => {
      Object.assign(state, action.payload)
    },
    
    // 更新尺寸设置
    updateDimensions: (state, action: PayloadAction<{
      width?: number
      height?: number
    }>) => {
      if (action.payload.width !== undefined) {
        state.width = Math.max(100, action.payload.width)
      }
      if (action.payload.height !== undefined) {
        state.height = Math.max(100, action.payload.height)
      }
    },
    
    // 重置到默认配置
    resetToDefaults: () => defaultWordCloudConfig,
    
    // 更新完整配置
    updateConfig: (state, action: PayloadAction<Partial<WordCloudConfig>>) => {
      Object.assign(state, action.payload)
    }
  }
})

// 导出actions
export const {
  updateFontSettings,
  updateFontSizeRange,
  updateRotationSettings,
  updateColorScheme,
  updateLayoutSettings,
  updateAnimationSettings,
  updateDimensions,
  resetToDefaults,
  updateConfig
} = wordCloudConfigSlice.actions

// 导出selectors
export const selectWordCloudConfig = (state: { wordCloudConfig: WordCloudConfig }) => state.wordCloudConfig
export const selectFontSettings = (state: { wordCloudConfig: WordCloudConfig }) => ({
  fontFamily: state.wordCloudConfig.fontFamily,
  fontStyle: state.wordCloudConfig.fontStyle,
  fontWeight: state.wordCloudConfig.fontWeight
})
export const selectColorSettings = (state: { wordCloudConfig: WordCloudConfig }) => ({
  colorScheme: state.wordCloudConfig.colorScheme,
  customColors: state.wordCloudConfig.customColors
})

export default wordCloudConfigSlice.reducer
